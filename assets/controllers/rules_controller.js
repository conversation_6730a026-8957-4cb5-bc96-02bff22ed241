import { Controller } from "@hotwired/stimulus"
import * as bootstrap from 'bootstrap'

export default class extends Controller {
    static targets = ["modal", "translations"]
    
    connect() {
        this.I18N = JSON.parse(this.translationsTarget.textContent)

        this.modalElement = this.modalTarget.querySelector('#ruleModal')

        if (!this.modalElement) {
            console.error('Modal element #ruleModal not found in modalTarget!')
            return
        }

        this.modal = new bootstrap.Modal(this.modalElement, { backdrop: 'static' })
        this.formRoot = this.modalElement.querySelector('#rule-form-root')

        // Load global data
        this.EVENTS = this.readGlobalJson('events-json') || []
        this.CONDITIONS = this.readGlobalJson('conditions-json') || []
        this.ACTIONS = this.readGlobalJson('actions-json') || []

        this.FILTERED_CONDITIONS = this.CONDITIONS
        this.FILTERED_ACTIONS = this.ACTIONS

        // Expose RuleForm API globally for compatibility
        this.setupGlobalAPI()
    }

    // Handle create rule button click
    createRule(event) {
        const createBtn = event.currentTarget

        window.RuleForm.openNew({
            container: this.formRoot,
            createUrl: createBtn.dataset.createUrl,
            defaults: {
                name: 'NoName',
                weight: 0,
            },
        })
        this.modal.show()
    }

    // Handle edit rule button click  
    editRule(event) {
        const editBtn = event.currentTarget
        window.RuleForm.openEdit({
            container: this.formRoot,
            editUrl: editBtn.dataset.editUrl,
            rule: JSON.parse(editBtn.getAttribute('data-rule')),
        })
        this.modal.show()
    }

    // Handle delete rule button click
    deleteRule(event) {
        const deleteBtn = event.currentTarget
        if (confirm('Na pewno chcesz usunąć?')) {
            fetch(deleteBtn.dataset.deleteUrl, {
                method: 'POST',
                headers: { 'X-Requested-With': 'XMLHttpRequest' },
            })
            .then((resp) => {
                if (resp.redirected) {
                    window.location.href = resp.url
                }
            })
            .catch((err) => console.error(err))
        }
    }

    // Utility functions
    createElement(tagName, attributes = {}, ...children) {
        const element = document.createElement(tagName)

        if (attributes) {
            Object.keys(attributes).forEach((attrKey) => {
                if (attrKey === 'dataset') {
                    Object.keys(attributes.dataset).forEach((dataKey) => {
                        element.dataset[dataKey] = attributes.dataset[dataKey]
                    })
                } else if (attrKey === 'class') {
                    element.className = attributes[attrKey]
                } else if (attrKey === 'style') {
                    Object.assign(element.style, attributes[attrKey])
                } else if (attrKey.startsWith('on') && typeof attributes[attrKey] === 'function') {
                    const eventType = attrKey.slice(2)
                    element.addEventListener(eventType, attributes[attrKey])
                } else {
                    element.setAttribute(attrKey, attributes[attrKey])
                }
            })
        }

        children.forEach((child) => {
            if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child))
            } else {
                element.appendChild(child)
            }
        })

        return element
    }

    ensureArray(value) {
        if (Array.isArray(value)) {
            return value
        }
        if (value) {
            return [value]
        }
        return []
    }

    getSelectedValues(selectElement) {
        const selectedOptions = Array.from(selectElement.selectedOptions)
        return selectedOptions.map((option) => option.value)
    }

    setValue(element, value) {
        if (!element) return

        if (element.tagName === 'SELECT' && element.multiple) {
            const desiredValues = Array.isArray(value) ? value.map(String) : [String(value)]
            Array.from(element.options).forEach((option) => {
                option.selected = desiredValues.includes(String(option.value))
            })
            return
        }

        if (element.tagName === 'SELECT') {
            element.value = (value ?? '').toString()
            return
        }

        if (element.type === 'number') {
            element.value = value === null || value === undefined || value === '' ? '' : Number(value)
            return
        }

        element.value = value === null || value === undefined ? '' : String(value)
    }

    readGlobalJson(elementId) {
        const targetElement = document.getElementById(elementId)
        return targetElement ? JSON.parse(targetElement.textContent) : null
    }

    allowedByContext(requiredKeys, providedKeys) {
        const req = Array.isArray(requiredKeys) ? requiredKeys : []
        if (req.length === 0) return true
        const provided = Array.isArray(providedKeys) ? providedKeys : []
        return req.every((k) => provided.includes(k))
    }

    applyFilterForEvent(eventName) {
        const ev = this.ensureArray(this.EVENTS).find((e) => e.name === eventName)
        const provided = ev && Array.isArray(ev.providedContext) ? ev.providedContext : []

        this.FILTERED_CONDITIONS = this.ensureArray(this.CONDITIONS).filter((c) => 
            this.allowedByContext(c.requiredContext, provided)
        )
        this.FILTERED_ACTIONS = this.ensureArray(this.ACTIONS).filter((a) => 
            this.allowedByContext(a.requiredContext, provided)
        )
    }

    rebuildSelectOptions(selectEl, items, placeholder) {
        const current = selectEl.value
        selectEl.innerHTML = ''
        selectEl.appendChild(this.createElement('option', { value: '' }, placeholder))
        items.forEach((it) => {
            selectEl.appendChild(this.createElement('option', { value: it.name }, it.label || it.name))
        })
        if (items.some((it) => it.name === current)) selectEl.value = current
    }

    buildEventSelect(selectElement) {
        selectElement.innerHTML = ''
        selectElement.appendChild(this.createElement('option', { value: '' }, `-- ${this.I18N?.placeholders?.chooseEvent} --`))

        this.ensureArray(this.EVENTS).forEach((eventItem) => {
            const optionValue = typeof eventItem === 'string' ? eventItem : eventItem.name || eventItem.value || ''
            const optionLabel = typeof eventItem === 'string' ? eventItem : eventItem.label || optionValue

            selectElement.appendChild(this.createElement('option', { value: optionValue }, optionLabel))
        })
    }

    normalizeFieldDefsFromCondition(def) {
        console.log('[normalizeCondition] raw def:', def)
        console.log('[normalizeCondition] def structure:', JSON.stringify(def, null, 2))

        // Sprawdź różne możliwe struktury
        if (def && def.settings) {
            console.log('[normalizeCondition] Found settings:', def.settings)
            const s = def.settings
            const list = Array.isArray(s) ? s : s.fields ? s.fields : []
            const out = []
            list.forEach((item) => {
                if (!item) return
                if (item.name || item.type || item.label || item.options || item.allowedOperators) {
                    out.push(item)
                }
                if (Array.isArray(item.fields)) {
                    out.push(...item.fields)
                }
            })
            console.log('[normalizeCondition] from settings ->', out)
            return out.filter(Boolean)
        }

        // Sprawdź allowedValues
        if (def && def.allowedValues) {
            console.log('[normalizeCondition] Found allowedValues:', def.allowedValues)
            const out = []
            const av = def.allowedValues
            const arr = Array.isArray(av) ? av : [av]
            arr.forEach((v) => {
                if (!v) return
                if (Array.isArray(v.fields)) {
                    console.log('[normalizeCondition] Adding fields from allowedValues:', v.fields)
                    out.push(...v.fields)
                } else {
                    console.log('[normalizeCondition] Adding direct value:', v)
                    out.push(v)
                }
            })
            console.log('[normalizeCondition] from allowedValues ->', out)
            return out.filter(Boolean)
        }

        // Sprawdź czy def samo w sobie ma fields
        if (def && def.fields) {
            console.log('[normalizeCondition] Found direct fields:', def.fields)
            return Array.isArray(def.fields) ? def.fields : [def.fields]
        }

        // Sprawdź czy def ma properties/parameters
        if (def && (def.properties || def.parameters)) {
            const props = def.properties || def.parameters
            console.log('[normalizeCondition] Found properties/parameters:', props)
            if (Array.isArray(props)) {
                return props
            }
            // Jeśli to obiekt, przekształć na array
            return Object.keys(props).map(key => ({
                name: key,
                ...props[key]
            }))
        }

        console.log('[normalizeCondition] No fields found, returning empty array')
        return []
    }

    normalizeFieldDefsFromAction(actionDefinition) {
        console.log('[normalizeAction] raw def:', actionDefinition)
        console.log('[normalizeAction] def structure:', JSON.stringify(actionDefinition, null, 2))

        // Sprawdź settings
        if (actionDefinition && actionDefinition.settings) {
            console.log('[normalizeAction] Found settings:', actionDefinition.settings)
            const s = actionDefinition.settings
            const list = Array.isArray(s) ? s : s.fields ? s.fields : []
            const out = []
            list.forEach((item) => {
                if (!item) return
                if (item.name || item.type || item.label || item.options) {
                    out.push(item)
                }
                if (Array.isArray(item.fields)) {
                    out.push(...item.fields)
                }
            })
            console.log('[normalizeAction] from settings ->', out)
            return out.filter(Boolean)
        }

        // Sprawdź allowedValues
        const allowedValues = actionDefinition?.allowedValues ?? []
        console.log('[normalizeAction] allowedValues:', allowedValues)

        if (allowedValues && allowedValues.fields) {
            console.log('[normalizeAction] Found direct fields in allowedValues:', allowedValues.fields)
            return allowedValues.fields
        }

        const normalizedFields = []

        this.ensureArray(allowedValues).forEach((value) => {
            if (value && value.fields) {
                console.log('[normalizeAction] Adding fields from value:', value.fields)
                normalizedFields.push(...value.fields)
            } else {
                console.log('[normalizeAction] Adding direct value:', value)
                normalizedFields.push(value)
            }
        })

        // Sprawdź czy actionDefinition samo w sobie ma fields
        if (actionDefinition && actionDefinition.fields) {
            console.log('[normalizeAction] Found direct fields:', actionDefinition.fields)
            return Array.isArray(actionDefinition.fields) ? actionDefinition.fields : [actionDefinition.fields]
        }

        // Sprawdź czy actionDefinition ma properties/parameters
        if (actionDefinition && (actionDefinition.properties || actionDefinition.parameters)) {
            const props = actionDefinition.properties || actionDefinition.parameters
            console.log('[normalizeAction] Found properties/parameters:', props)
            if (Array.isArray(props)) {
                return props
            }
            // Jeśli to obiekt, przekształć na array
            return Object.keys(props).map(key => ({
                name: key,
                ...props[key]
            }))
        }

        console.log('[normalizeAction] Final normalized fields:', normalizedFields)
        return normalizedFields.filter(Boolean)
    }

    makeOperatorSelect(allowedOperators) {
        const operatorSelect = this.createElement('select', {
            class: 'form-select',
            'data-role': 'operator',
        })

        const operatorsList = allowedOperators?.options ?? this.ensureArray(allowedOperators)

        operatorsList.forEach((operatorItem) => {
            const optionValue = operatorItem && typeof operatorItem === 'object' ? operatorItem.value ?? operatorItem : operatorItem
            const optionLabel = operatorItem && typeof operatorItem === 'object' ? operatorItem.label ?? String(optionValue) : String(optionValue)

            operatorSelect.appendChild(this.createElement('option', { value: optionValue }, optionLabel))
        })

        return operatorSelect
    }

    makeValueInput(fieldDef) {
        console.log('[makeValueInput] Creating input for field:', fieldDef)
        const fieldType = fieldDef.type || 'text'
        console.log('[makeValueInput] Field type:', fieldType)

        if (fieldType === 'select' || fieldType === 'selectMultiple') {
            console.log('[makeValueInput] Creating select with options:', fieldDef.options)
            const selectElement = this.createElement('select', {
                class: 'form-select',
                'data-role': 'value',
            })

            if (fieldType === 'selectMultiple') {
                selectElement.multiple = true
            }

            (fieldDef.options || []).forEach((optionItem) => {
                const optionValue = optionItem && typeof optionItem === 'object' ? optionItem.value ?? optionItem : optionItem
                const optionLabel = optionItem && typeof optionItem === 'object' ? optionItem.label ?? String(optionValue) : String(optionValue)

                selectElement.appendChild(this.createElement('option', { value: optionValue }, optionLabel))
            })

            return selectElement
        }

        if (fieldType === 'number') {
            console.log('[makeValueInput] Creating number input')
            const numberInput = this.createElement('input', {
                type: 'number',
                class: 'form-control',
                'data-role': 'value',
            })

            if (fieldDef.min !== undefined) numberInput.min = fieldDef.min
            if (fieldDef.max !== undefined) numberInput.max = fieldDef.max

            return numberInput
        }

        console.log('[makeValueInput] Creating text input')
        return this.createElement('input', {
            type: 'text',
            class: 'form-control',
            'data-role': 'value',
        })
    }

    buildFieldRow(fieldDef, includeOperator) {
        console.log('[buildFieldRow] Building field row for:', fieldDef)
        console.log('[buildFieldRow] Include operator:', includeOperator)

        if (!fieldDef || !fieldDef.name) {
            console.error('[buildFieldRow] Invalid fieldDef:', fieldDef)
            return this.createElement('div', { class: 'alert alert-danger' }, 'Invalid field definition')
        }

        const fieldRow = this.createElement('div', {
            class: 'row g-2 align-items-center mb-2',
            dataset: {
                fieldName: fieldDef.name,
                fieldType: fieldDef.type || 'text',
                withOperator: includeOperator ? '1' : '0',
            },
        })

        const labelColumn = this.createElement('div', { class: 'col-md-3' },
            this.createElement('label', { class: 'form-label mb-0' }, fieldDef.label || fieldDef.name)
        )

        const valueColumn = this.createElement('div', { class: 'col-md-6' })
        const valueInput = this.makeValueInput(fieldDef)
        console.log('[buildFieldRow] Created value input:', valueInput)
        valueColumn.appendChild(valueInput)

        fieldRow.appendChild(labelColumn)
        fieldRow.appendChild(valueColumn)

        if (includeOperator && fieldDef.allowedOperators) {
            console.log('[buildFieldRow] Adding operator column with operators:', fieldDef.allowedOperators)
            const operatorColumn = this.createElement('div', { class: 'col-md-3' })
            operatorColumn.appendChild(this.makeOperatorSelect(fieldDef.allowedOperators))
            fieldRow.appendChild(operatorColumn)
        }

        console.log('[buildFieldRow] Final field row:', fieldRow)
        return fieldRow
    }

    buildConditionBlock() {
        const conditionBlock = this.createElement('div', {
            class: 'border rounded p-2 mb-2',
            dataset: { type: 'condition' },
        })

        const blockHeader = this.createElement('div', { class: 'd-flex gap-2 align-items-center mb-2' })

        const conditionSelect = this.createElement('select', { class: 'form-select' })
        conditionSelect.appendChild(this.createElement('option', { value: '' }, `-- ${this.I18N?.placeholders?.chooseCondition} --`))

        this.ensureArray(this.FILTERED_CONDITIONS).forEach((conditionDef) => {
            conditionSelect.appendChild(this.createElement('option', { value: conditionDef.name }, conditionDef.label || conditionDef.name))
        })

        const removeButton = this.createElement(
            'button',
            {
                type: 'button',
                class: 'btn btn-sm btn-outline-danger ms-2',
                onclick: () => conditionBlock.remove(),
            },
            this.createElement('i', { class: 'fa fa-trash me-2' }),
            this.I18N.common.remove
        )

        blockHeader.appendChild(conditionSelect)
        blockHeader.appendChild(removeButton)

        const fieldsContainer = this.createElement('div', { class: 'mt-2' })

        conditionSelect.addEventListener('change', () => {
            console.log('Condition select changed to:', conditionSelect.value)
            fieldsContainer.innerHTML = ''

            const selectedDefinition = this.ensureArray(this.CONDITIONS).find((conditionDef) => conditionDef.name === conditionSelect.value)
            console.log('Selected condition definition:', selectedDefinition)

            if (!selectedDefinition) {
                console.log('No condition definition found!')
                return
            }

            const normalizedFields = this.normalizeFieldDefsFromCondition(selectedDefinition)
            console.log('Normalized fields:', normalizedFields)

            normalizedFields.forEach((fieldDef) => {
                console.log('Creating field row for:', fieldDef)
                const fieldRow = this.buildFieldRow(fieldDef, !!fieldDef.allowedOperators)
                fieldsContainer.appendChild(fieldRow)
            })

            console.log('Fields container after adding fields:', fieldsContainer.innerHTML)
        })

        conditionBlock.appendChild(blockHeader)
        conditionBlock.appendChild(fieldsContainer)

        return conditionBlock
    }

    buildActionBlock() {
        const actionBlock = this.createElement('div', {
            class: 'border rounded p-2 mb-2',
            dataset: { type: 'action' },
        })

        const blockHeader = this.createElement('div', { class: 'd-flex gap-2 align-items-center mb-2' })

        const actionSelect = this.createElement('select', { class: 'form-select' })
        actionSelect.appendChild(this.createElement('option', { value: '' }, `-- ${this.I18N?.placeholders?.chooseAction} --`))

        this.ensureArray(this.FILTERED_ACTIONS).forEach((actionDef) => {
            actionSelect.appendChild(this.createElement('option', { value: actionDef.name }, actionDef.label || actionDef.name))
        })

        const removeButton = this.createElement(
            'button',
            {
                type: 'button',
                class: 'btn btn-sm btn-outline-danger ms-2',
                onclick: () => actionBlock.remove(),
            },
            this.createElement('i', { class: 'fa fa-trash me-2' }),
            this.I18N.common.remove
        )

        blockHeader.appendChild(actionSelect)
        blockHeader.appendChild(removeButton)

        const fieldsContainer = this.createElement('div', { class: 'mt-2' })

        actionSelect.addEventListener('change', () => {
            console.log('Action select changed to:', actionSelect.value)
            fieldsContainer.innerHTML = ''

            const selectedDefinition = this.ensureArray(this.ACTIONS).find((actionDef) => actionDef.name === actionSelect.value)
            console.log('Selected action definition:', selectedDefinition)

            if (!selectedDefinition) {
                console.log('No action definition found!')
                return
            }

            const normalizedFields = this.normalizeFieldDefsFromAction(selectedDefinition)
            console.log('Normalized action fields:', normalizedFields)

            normalizedFields.forEach((fieldDef) => {
                console.log('Creating action field row for:', fieldDef)
                const fieldRow = this.buildFieldRow(fieldDef, false)
                fieldsContainer.appendChild(fieldRow)
            })

            console.log('Action fields container after adding fields:', fieldsContainer.innerHTML)
        })

        actionBlock.appendChild(blockHeader)
        actionBlock.appendChild(fieldsContainer)

        return actionBlock
    }

    // Serialization functions
    readConditions(container) {
        const conditionsList = []

        container.querySelectorAll('[data-type="condition"]').forEach((conditionBlock) => {
            const conditionSelect = conditionBlock.querySelector('select')

            if (!conditionSelect || !conditionSelect.value) return

            const conditionFields = []

            conditionBlock.querySelectorAll('[data-field-name]').forEach((fieldRow) => {
                const fieldName = fieldRow.dataset.fieldName
                const hasOperator = fieldRow.dataset.withOperator === '1'

                const valueElement = fieldRow.querySelector('[data-role="value"]')
                const operatorElement = fieldRow.querySelector('[data-role="operator"]')

                let fieldValue = null

                if (valueElement) {
                    if (valueElement.tagName === 'SELECT' && valueElement.multiple) {
                        fieldValue = this.getSelectedValues(valueElement)
                    } else if (valueElement.type === 'number') {
                        fieldValue = valueElement.value ? Number(valueElement.value) : null
                    } else {
                        fieldValue = valueElement.value || null
                    }
                }

                const fieldData = {
                    name: fieldName,
                    value: fieldValue,
                }

                if (hasOperator && operatorElement && operatorElement.value) {
                    fieldData.operator = operatorElement.value
                }

                conditionFields.push(fieldData)
            })

            conditionsList.push({
                name: conditionSelect.value,
                fields: conditionFields,
            })
        })

        return conditionsList
    }

    readActions(container) {
        const actionsList = []

        container.querySelectorAll('[data-type="action"]').forEach((actionBlock) => {
            const actionSelect = actionBlock.querySelector('select')

            if (!actionSelect || !actionSelect.value) return

            const actionFields = {}

            actionBlock.querySelectorAll('[data-field-name]').forEach((fieldRow) => {
                const fieldName = fieldRow.dataset.fieldName
                const valueElement = fieldRow.querySelector('[data-role="value"]')

                let fieldValue = null

                if (valueElement) {
                    if (valueElement.tagName === 'SELECT' && valueElement.multiple) {
                        fieldValue = this.getSelectedValues(valueElement)
                    } else if (valueElement.type === 'number') {
                        fieldValue = valueElement.value ? Number(valueElement.value) : null
                    } else {
                        fieldValue = valueElement.value || null
                    }
                }

                actionFields[fieldName] = fieldValue
            })

            actionsList.push({
                name: actionSelect.value,
                fields: actionFields,
            })
        })

        return actionsList
    }

    initCommon(container) {
        const eventSelectElement = container.querySelector('#eventSelect')
        const addConditionButton = container.querySelector('#addConditionBtn')
        const addActionButton = container.querySelector('#addActionBtn')
        const conditionsContainerElement = container.querySelector('#conditionsContainer')
        const actionsContainerElement = container.querySelector('#actionsContainer')

        this.buildEventSelect(eventSelectElement)

        addConditionButton.disabled = true
        addActionButton.disabled = true

        eventSelectElement.addEventListener('change', () => {
            const evName = eventSelectElement.value || ''
            this.applyFilterForEvent(evName)

            this.refreshExistingBlocks(conditionsContainerElement, actionsContainerElement)
            this.clearActionsAndConditions()

            const enabled = !!evName
            addConditionButton.disabled = !enabled
            addActionButton.disabled = !enabled
        })

        addConditionButton.onclick = () => {
            const newConditionBlock = this.buildConditionBlock()
            conditionsContainerElement.appendChild(newConditionBlock)
        }

        addActionButton.onclick = () => {
            const newActionBlock = this.buildActionBlock()
            actionsContainerElement.appendChild(newActionBlock)
        }
    }

    hydrate(container, rule) {
        container.querySelector('#ruleName').value = rule.name ?? 'NoName'
        container.querySelector('#ruleWeight').value = rule.weight ?? 0

        const eventSelectElement = container.querySelector('#eventSelect')
        this.setValue(eventSelectElement, rule.event || '')

        this.applyFilterForEvent(rule.event || '')

        const conditionsContainerElement = container.querySelector('#conditionsContainer')
        const actionsContainerElement = container.querySelector('#actionsContainer')

        conditionsContainerElement.innerHTML = ''
        actionsContainerElement.innerHTML = ''

        this.ensureArray(rule.conditions).forEach((condition) => {
            const conditionBlockElement = this.buildConditionBlock()
            conditionsContainerElement.appendChild(conditionBlockElement)

            const conditionNameSelectElement = conditionBlockElement.querySelector('select')
            if (conditionNameSelectElement) {
                this.setValue(conditionNameSelectElement, condition.name || '')
                // Trigger change event to load fields
                const changeEvent = new Event('change', { bubbles: true })
                conditionNameSelectElement.dispatchEvent(changeEvent)

                // Wait a bit for the fields to be created, then populate them
                setTimeout(() => {
                    (condition.fields || []).forEach((field) => {
                        const fieldRowElement = Array.from(conditionBlockElement.querySelectorAll('[data-field-name]')).find((row) => row.dataset.fieldName === field.name)

                        if (!fieldRowElement) return

                        this.setValue(fieldRowElement.querySelector('[data-role="value"]'), field.value)

                        const operatorElement = fieldRowElement.querySelector('[data-role="operator"]')
                        if (operatorElement && field.operator) {
                            this.setValue(operatorElement, field.operator)
                        }
                    })
                }, 10)
            }
        })

        this.ensureArray(rule.actions).forEach((action) => {
            const actionBlockElement = this.buildActionBlock()
            actionsContainerElement.appendChild(actionBlockElement)

            const actionNameSelectElement = actionBlockElement.querySelector('select')
            if (actionNameSelectElement) {
                this.setValue(actionNameSelectElement, action.name || '')
                // Trigger change event to load fields
                const changeEvent = new Event('change', { bubbles: true })
                actionNameSelectElement.dispatchEvent(changeEvent)

                // Wait a bit for the fields to be created, then populate them
                setTimeout(() => {
                    const actionFields = action.fields || {}
                    Object.keys(actionFields).forEach((fieldName) => {
                        const fieldRowElement = Array.from(actionBlockElement.querySelectorAll('[data-field-name]')).find((row) => row.dataset.fieldName === fieldName)

                        if (!fieldRowElement) return

                        this.setValue(fieldRowElement.querySelector('[data-role="value"]'), actionFields[fieldName])
                    })
                }, 10)
            }
        })
    }

    wireSubmit(container, formActionUrl) {
        const formElement = container.querySelector('#ruleForm')
        const hiddenPayloadInput = container.querySelector('#payload')

        formElement.setAttribute('action', formActionUrl)

        formElement.onsubmit = (event) => {
            const nameValue = container.querySelector('#ruleName').value || 'NoName'
            const eventValue = container.querySelector('#eventSelect').value || ''
            const weightValue = Number(container.querySelector('#ruleWeight').value) || 0

            const conditionsContainerElement = container.querySelector('#conditionsContainer')
            const actionsContainerElement = container.querySelector('#actionsContainer')

            const conditionsPayload = this.readConditions(conditionsContainerElement)
            const actionsPayload = this.readActions(actionsContainerElement)

            if (!eventValue) {
                alert('Proszę wybrać event przed zapisaniem reguły.')
                event.preventDefault()
                return false
            }

            const payload = {
                name: nameValue,
                event: eventValue,
                weight: weightValue,
                conditions: conditionsPayload,
                actions: actionsPayload,
            }

            hiddenPayloadInput.value = JSON.stringify(payload)
        }
    }

    refreshExistingBlocks(conditionsContainerElement, actionsContainerElement) {
        conditionsContainerElement.querySelectorAll('[data-type="condition"] select').forEach((sel) => {
            this.rebuildSelectOptions(sel, this.FILTERED_CONDITIONS, `-- ${this.I18N?.placeholders?.chooseCondition} --`)
        })
        actionsContainerElement.querySelectorAll('[data-type="action"] select').forEach((sel) => {
            this.rebuildSelectOptions(sel, this.FILTERED_ACTIONS, `-- ${this.I18N?.placeholders?.chooseAction} --`)
        })
    }

    clearActionsAndConditions() {
        this.modalElement.querySelectorAll('[data-type="condition"]').forEach((el) => el.remove())
        this.modalElement.querySelectorAll('[data-type="action"]').forEach((el) => el.remove())
    }

    setupGlobalAPI() {
        const self = this

        window.RuleForm = {
            openNew({ container, createUrl, defaults = {} }) {
                self.initCommon(container)

                self.hydrate(container, {
                    name: defaults.name ?? 'NoName',
                    event: '',
                    conditions: [],
                    actions: [],
                    weight: defaults.weight ?? 0,
                })

                self.wireSubmit(container, createUrl)

                const modalTitleElement = container.closest('.modal-content').querySelector('.modal-title')
                if (modalTitleElement) {
                    modalTitleElement.textContent = self.I18N?.modal?.title?.create || 'Create Rule'
                }
            },

            openEdit({ container, editUrl, rule }) {
                self.initCommon(container)
                self.hydrate(container, rule || {})
                self.wireSubmit(container, editUrl)

                const modalTitleElement = container.closest('.modal-content').querySelector('.modal-title')
                if (modalTitleElement) {
                    modalTitleElement.textContent = self.I18N?.modal?.title?.edit || 'Edit Rule'
                }
            }
        }
    }
}
